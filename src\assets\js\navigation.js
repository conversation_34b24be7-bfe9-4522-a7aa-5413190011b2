// AIMS Modern Navigation System
// Clean, intuitive navigation with smooth animations

const ModernNavigation = {
  // Navigation state
  state: {
    activeSection: null,
    activePage: 'dashboard',
    expandedSections: new Set(),
    sidebarCollapsed: false
  },

  // Initialize navigation
  init() {
    this.renderNavigation();
    this.initializeEventListeners();
    this.setActivePage('dashboard');
  },

  // Get navigation menu structure
  getMenuStructure() {
    return [
      {
        type: 'item',
        id: 'dashboard',
        title: 'Dashboard',
        icon: 'fas fa-tachometer-alt',
        page: 'dashboard',
        badge: null
      },
      {
        type: 'section',
        id: 'student-management',
        title: 'Students',
        icon: 'fas fa-user-graduate',
        items: [
          { title: 'Register Student', page: 'register-student', icon: 'fas fa-user-plus', description: 'Add new students' },
          { title: 'Manage Students', page: 'manage-students', icon: 'fas fa-users', description: 'View and edit student records' }
        ]
      },
      {
        type: 'section',
        id: 'classes-management',
        title: 'Classes',
        icon: 'fas fa-door-open',
        items: [
          { title: 'Streams', page: 'manage-streams', icon: 'fas fa-users', description: ' Manage class streams' },
          { title: 'Enrollments', page: 'enroll-students', icon: 'fas fa-user-check', description: 'Enroll students in classes' }
        ]
      },
      {
        type: 'section',
        id: 'teacher-management',
        title: 'Teachers',
        icon: 'fas fa-chalkboard-teacher',
        items: [
          { title: 'Register Teacher', page: 'register-teacher', icon: 'fas fa-user-plus' },
          { title: 'Manage Teachers', page: 'manage-teachers', icon: 'fas fa-users' }
        ]
      },


      {
        type: 'section',
        id: 'assessment-management',
        title: 'Assessments',
        icon: 'fas fa-clipboard-check',
        items: [
          { title: 'Enter CA Scores', page: 'enter-ca-scores', icon: 'fas fa-pen', description: 'Input continuous assessment scores' },
          { title: 'Enter Exam Marks', page: 'enter-exam-grades', icon: 'fas fa-pencil-alt', description: 'Input examination marks' }
        ]
      },
      {
        type: 'section',
        id: 'grade-boundaries',
        title: 'Grade Boundaries',
        icon: 'fas fa-chart-line',
        items: [
          { title: 'O-Level', page: 'o-level-grade-boundaries', icon: 'fas fa-chart-bar' },
          { title: 'A-Level', page: 'a-level-grade-boundaries', icon: 'fas fa-chart-area' }
        ]
      },
      {
        type: 'section',
        id: 'report-cards',
        title: 'Report Card',
        icon: 'fas fa-chart-pie',
        items: [
          { title: 'Generate', page: 'generate-report-cards', icon: 'fas fa-file-pdf', description: 'Create student report cards' },
        ]
      },
      {
        type: 'section',
        id: 'system-administration',
        title: 'System Users',
        icon: 'fas fa-users-cog',
        items: [
          { title: 'Register', page: 'register-admin', icon: 'fas fa-user-shield', description: 'Add new system administrators' },
          { title: 'Manage', page: 'manage-admins', icon: 'fas fa-users-cog', description: 'Manage system users' },
        ]
      },
      {
        type: 'section',
        id: 'system-settings',
        title: 'System Settings',
        icon: 'fas fa-cogs',
        items: [
          { title: 'Academic Years', page: 'academic-years-management', icon: 'fas fa-calendar-alt', description: 'Manage academic years and terms' },
          { title: 'School Settings', page: 'school-settings', icon: 'fas fa-school', description: 'Configure school information' },
          { title: 'Import/Export Data', page: 'data-import-export', icon: 'fas fa-exchange-alt', description: 'Data import/export tools' },
        ]
      }
    ];
  },

  // Render navigation menu
  renderNavigation() {
    const container = document.getElementById('navigation-menu');
    if (!container) return;

    const menuStructure = this.getMenuStructure();
    let html = '';

    menuStructure.forEach(item => {
      if (item.type === 'item') {
        html += this.renderMenuItem(item);
      } else if (item.type === 'section') {
        html += this.renderMenuSection(item);
      }
    });

    container.innerHTML = html;
  },

  // Render single menu item
  renderMenuItem(item) {
    const isActive = this.state.activePage === item.page;
    const isCollapsed = this.state.sidebarCollapsed;

    return `
      <div class="nav-item ${isActive ? 'active' : ''}" data-page="${item.page}" title="${isCollapsed ? item.title : ''}">
        <button class="w-full flex items-center ${isCollapsed ? 'justify-center px-2' : 'space-x-3 px-3'} py-2.5 rounded-lg text-left transition-all duration-200 hover:bg-gray-100 ${isActive ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-700'}">
          <i class="${item.icon} text-sm flex-shrink-0"></i>
          <span class="font-medium nav-text transition-opacity duration-300 ${isCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'}">${item.title}</span>
        </button>
      </div>
    `;
  },

  // Render menu section with collapsible items
  renderMenuSection(section) {
    const isExpanded = this.state.expandedSections.has(section.id);
    const hasActiveItem = section.items.some(item => item.page === this.state.activePage);
    const isCollapsed = this.state.sidebarCollapsed;

    let html = `
      <div class="nav-section mb-2" data-section="${section.id}" title="${isCollapsed ? section.title : ''}">
        <button class="w-full flex items-center ${isCollapsed ? 'justify-center px-2' : 'justify-between px-3'} py-2.5 rounded-lg text-left transition-all duration-200 hover:bg-gray-50 hover:shadow-sm text-gray-700 font-medium ${hasActiveItem ? 'bg-blue-50 text-blue-700' : ''}">
          <div class="flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'}">
            <i class="${section.icon} text-sm ${hasActiveItem ? 'text-blue-600' : 'text-gray-500'} flex-shrink-0"></i>
            <span class="nav-text transition-opacity duration-300 ${isCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'}">${section.title}</span>
          </div>
          <i class="fas fa-chevron-down text-xs transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''} ${hasActiveItem ? 'text-blue-600' : 'text-gray-400'} nav-text ${isCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'}"></i>
        </button>
        <div class="nav-section-items ${isExpanded && !isCollapsed ? 'expanded' : 'collapsed'} ${isCollapsed ? '' : 'ml-4'} mt-1 space-y-1 overflow-hidden transition-all duration-300 ${isExpanded && !isCollapsed ? 'max-h-96' : 'max-h-0'}">
    `;

    if (!isCollapsed) {
      section.items.forEach(item => {
        const isActive = this.state.activePage === item.page;
        html += `
          <div class="nav-item ${isActive ? 'active' : ''}" data-page="${item.page}">
            <button class="w-full flex items-center space-x-3 px-4 py-2.5 rounded-lg text-left transition-all duration-200 hover:bg-gray-50 hover:shadow-sm ${isActive ? 'bg-blue-100 text-blue-700 border-l-3 border-blue-600 font-medium' : 'text-gray-600 hover:text-gray-800'} text-sm">
              <i class="${item.icon} text-xs ${isActive ? 'text-blue-600' : 'text-gray-400'} flex-shrink-0"></i>
              <span class="nav-text">${item.title}</span>
            </button>
          </div>
        `;
      });
    }

    html += `
        </div>
      </div>
    `;

    // Auto-expand section if it has active item and sidebar is not collapsed
    if (hasActiveItem && !isExpanded && !isCollapsed) {
      this.state.expandedSections.add(section.id);
    }

    return html;
  },

  // Initialize event listeners
  initializeEventListeners() {
    const container = document.getElementById('navigation-menu');
    if (!container) return;

    // Handle menu item clicks
    container.addEventListener('click', (e) => {
      const navItem = e.target.closest('.nav-item');
      const navSection = e.target.closest('.nav-section');

      if (navItem) {
        const page = navItem.dataset.page;
        if (page) {
          this.navigateToPage(page);
        }
      } else if (navSection && !navItem) {
        const sectionId = navSection.dataset.section;
        if (sectionId) {
          this.toggleSection(sectionId);
        }
      }
    });
  },

  // Navigate to page
  navigateToPage(page) {
    this.setActivePage(page);

    // Load page content using PageRouter
    if (window.PageRouter) {
      window.PageRouter.loadPage(page);
    } else if (window.Layout) {
      window.Layout.loadPage(page);
    }
  },

  // Set active page
  setActivePage(page) {
    // Remove active class from all items
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
      const button = item.querySelector('button');
      button.classList.remove('bg-blue-50', 'text-blue-700', 'border-r-2', 'border-blue-600');
      button.classList.add('text-gray-700');
    });

    // Add active class to current page
    const activeItem = document.querySelector(`[data-page="${page}"]`);
    if (activeItem) {
      activeItem.classList.add('active');
      const button = activeItem.querySelector('button');
      button.classList.add('bg-blue-50', 'text-blue-700', 'border-r-2', 'border-blue-600');
      button.classList.remove('text-gray-700', 'text-gray-600');
    }

    this.state.activePage = page;
  },

  // Toggle section expansion with auto-close functionality
  toggleSection(sectionId) {
    // Don't allow section expansion when sidebar is collapsed
    if (this.state.sidebarCollapsed) return;

    const section = document.querySelector(`[data-section="${sectionId}"]`);
    if (!section) return;

    const itemsContainer = section.querySelector('.nav-section-items');
    const chevron = section.querySelector('.fa-chevron-down');

    if (this.state.expandedSections.has(sectionId)) {
      // Collapse the clicked section
      this.state.expandedSections.delete(sectionId);
      itemsContainer.classList.remove('expanded');
      itemsContainer.classList.add('collapsed');
      chevron.classList.remove('rotate-180');
    } else {
      // Auto-close all other expanded sections first
      this.closeAllSections();

      // Then expand the clicked section
      this.state.expandedSections.add(sectionId);
      itemsContainer.classList.remove('collapsed');
      itemsContainer.classList.add('expanded');
      chevron.classList.add('rotate-180');
    }
  },

  // Close all expanded sections
  closeAllSections() {
    this.state.expandedSections.forEach(sectionId => {
      const section = document.querySelector(`[data-section="${sectionId}"]`);
      if (section) {
        const itemsContainer = section.querySelector('.nav-section-items');
        const chevron = section.querySelector('.fa-chevron-down');

        if (itemsContainer && chevron) {
          itemsContainer.classList.remove('expanded');
          itemsContainer.classList.add('collapsed');
          chevron.classList.remove('rotate-180');
        }
      }
    });
    this.state.expandedSections.clear();
  },

  // Handle sidebar toggle from layout component
  handleSidebarToggle(isCollapsed) {
    this.state.sidebarCollapsed = isCollapsed;

    // Re-render navigation to update collapsed state
    this.renderNavigation();

    // If collapsing, close all expanded sections
    if (isCollapsed) {
      this.state.expandedSections.clear();
    }
  }
};

// Export to global scope
window.ModernNavigation = ModernNavigation;
