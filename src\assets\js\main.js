// AIMS - Main Application JavaScript
// Academic Information Management System

// Global application state for Electron Desktop Application
window.AIMS = {
  currentUser: null,
  currentAcademicYear: null,
  currentTerm: null,
  serverUrl: 'http://localhost:3001', // Express server running on port 3001
  isInitialized: false,
  isElectron: true, // Flag to indicate this is an Electron app
  config: {
    // Environment configuration for Electron
    API_BASE_URL: 'http://localhost:3001/api',
    SERVER_URL: 'http://localhost:3001',
    DEBUG_MODE: 'true',
    NODE_ENV: 'development'
  }
};

// Application initialization
document.addEventListener('DOMContentLoaded', async function() {
  // Initialize modern components first
  initializeModernComponents();

  // Verify all UI/UX components are loaded
  verifyUIComponents();

  // Set a fallback timeout to ensure login screen is shown if initialization hangs
  const fallbackTimeout = setTimeout(() => {
    showLoginScreen();
  }, 15000); // 15 second fallback

  try {
    // Show loading screen
    showLoadingScreen('Initializing system...');

    // Check server connection (non-blocking)
    const serverConnected = await checkServerConnection();
    if (serverConnected) {
      // Check database status via API if needed
      await initializeDatabaseViaAPI();
    }

    // Check authentication
    console.log('🔐 Checking authentication...');
    await checkAuthentication();
    console.log('✅ Authentication check complete');

    // Initialize application
    console.log('🚀 Initializing application...');
    await initializeApplication();

    console.log('✅ AIMS Application Started Successfully');
    clearTimeout(fallbackTimeout); // Clear fallback if successful

  } catch (error) {
    console.error('❌ Application initialization failed:', error);

    // Clear the fallback timeout
    clearTimeout(fallbackTimeout);

    // Always show login screen if initialization fails
    console.log('🔄 Falling back to login screen...');
    showLoginScreen();

    // Show error notification if available
    if (window.showNotification) {
      showNotification('System initialization failed. Please try logging in.', 'warning');
    }
  }
});

// Verify all UI components are loaded
function verifyUIComponents() {
  const components = [
    { name: 'Layout', obj: window.Layout },
    { name: 'PageRouter', obj: window.PageRouter },
    { name: 'Dashboard', obj: window.Dashboard }
  ];

  let allLoaded = true;
  components.forEach(component => {
    if (!component.obj) {
      console.error(`❌ ${component.name} - Not loaded`);
      allLoaded = false;
    }
  });

  if (!allLoaded) {
    console.warn('⚠️ Some components failed to load');
  }
}

// Check server connection
async function checkServerConnection() {
  try {
    updateLoadingText('Connecting to server...');

    // Add timeout to prevent hanging
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${AIMS.serverUrl}/health`, {
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error('Server is not responding');
    }

    const health = await response.json();
    console.log('📡 Server connection established:', health);

    // Handle database initialization status
    if (health.status === 'INITIALIZING') {
      updateLoadingText('Setting up database for first time...');
      console.log('📊 Database is being initialized...');

      // Wait a bit and try again (with retry limit)
      await new Promise(resolve => setTimeout(resolve, 3000));
      return await checkServerConnection(); // Retry
    }

    if (!health.database.connected) {
      throw new Error('Database connection failed');
    }

    // Update current academic info
    if (health.database.stats) {
      AIMS.currentAcademicYear = health.database.stats.currentAcademicYear;
      AIMS.currentTerm = health.database.stats.currentTerm;

      // Update navbar immediately if elements exist
      updateAcademicInfoDisplay();
    }

  } catch (error) {
    console.warn('⚠️ Server connection failed, continuing in offline mode:', error);
    return false;
  }
}

// Initialize database via API (for frontend)
async function initializeDatabaseViaAPI() {
  try {
    updateLoadingText('Checking database status...');

    const response = await fetch(`${AIMS.serverUrl}/api/init-database`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      // If API is not available, database might already be initialized
      console.log('ℹ️ Database API not available, assuming database is ready');
      return;
    }

    const result = await response.json();

    if (!result.success) {
      console.warn('⚠️ Database initialization API returned error:', result.error);
      // Don't throw - database might already be initialized
      return;
    }

    console.log('📊 Database status checked:', result.message);

  } catch (error) {
    console.warn('⚠️ Database initialization check failed:', error.message);
    // Don't throw error for database initialization - it might already be initialized
    // Just log the error and continue
    console.log('ℹ️ Continuing despite database check error - database may already be ready');
  }
}

// Check authentication
async function checkAuthentication() {
  try {
    updateLoadingText('Checking authentication...');

    const token = localStorage.getItem('aims_token');
    if (!token) {
      console.log('ℹ️ No authentication token found - showing login screen');
      showLoginScreen();
      return;
    }

    // Validate token with server
    const response = await fetch(`${AIMS.serverUrl}/api/auth/validate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const userData = await response.json();
      if (userData.success && userData.user) {
        AIMS.currentUser = userData.user;
        console.log('✅ User authenticated:', userData.user.username);
        console.log('👤 User data loaded:', userData.user);

        // Enable application menu for authenticated user
        if (window.require) {
          const { ipcRenderer } = window.require('electron');
          await ipcRenderer.invoke('login-success');
        }

        await showMainApplication();

        // Update user interface with the loaded user data
        updateUserInterface();

        // Note: Academic year check is handled in auth.js after login
      } else {
        console.log('ℹ️ Invalid user data received');
        localStorage.removeItem('aims_token');
        showLoginScreen();
      }
    } else {
      console.log('ℹ️ Token validation failed - showing login screen');
      localStorage.removeItem('aims_token');
      showLoginScreen();
    }

  } catch (error) {
    console.error('❌ Authentication check failed:', error);
    console.log('🔄 Showing login screen due to authentication error');
    localStorage.removeItem('aims_token');
    showLoginScreen();
  }
}

// Initialize main application
async function initializeApplication() {
  if (!AIMS.currentUser) {
    return; // Will show login screen
  }

  try {
    updateLoadingText('Loading application...');

    // Update UI with current user info
    updateUserInterface();

    // Initialize authentication components
    if (window.AuthManager) {
      AuthManager.updateUserInterface();
    }


    // Mark as initialized
    AIMS.isInitialized = true;

    // Initialize page router after a short delay
    setTimeout(() => {
      if (window.PageRouter) {
        PageRouter.init();
      }
    }, 500);

    // Hide loading screen
    hideLoadingScreen();

  } catch (error) {
    console.error('❌ Application initialization failed:', error);
    throw error;
  }
}

// Show loading screen
function showLoadingScreen(message = 'Loading...') {
  const loadingScreen = document.getElementById('loading-screen');
  const loginScreen = document.getElementById('login-screen');
  const mainApp = document.getElementById('main-app');
  
  loadingScreen.style.display = 'flex';
  loginScreen.style.display = 'none';
  mainApp.style.display = 'none';
  
  updateLoadingText(message);
}

// Update loading text
function updateLoadingText(text) {
  const loadingMessage = document.getElementById('loading-message');
  if (loadingMessage) {
    loadingMessage.textContent = text;
  }
}

// Hide loading screen
function hideLoadingScreen() {
  const loadingScreen = document.getElementById('loading-screen');
  if (loadingScreen) {
    loadingScreen.style.display = 'none';
    console.log('✅ Loading screen hidden');
  } else {
    console.warn('⚠️ Loading screen element not found');
  }
}

// Show login screen
function showLoginScreen() {
  console.log('🔐 Showing login screen...');

  // Check if we're in the app layout (after login) or original layout (initial load)
  const appLayout = document.getElementById('app-layout');

  if (appLayout) {
    // We're in app layout - need to restore original login screen
    console.log('🔄 Restoring login screen from app layout...');
    restoreLoginScreen();
  } else {
    // We're in original layout - use original elements
    const loadingScreen = document.getElementById('loading-screen');
    const loginScreen = document.getElementById('login-screen');
    const mainApp = document.getElementById('main-app');

    if (!loadingScreen || !loginScreen || !mainApp) {
      console.error('❌ Required screen elements not found!', {
        loadingScreen: !!loadingScreen,
        loginScreen: !!loginScreen,
        mainApp: !!mainApp
      });
      // Fallback: reload the page to get back to login
      console.log('🔄 Falling back to page reload...');
      window.location.reload();
      return;
    }

    loadingScreen.style.display = 'none';
    loginScreen.style.display = 'flex';
    mainApp.style.display = 'none';
  }

  console.log('✅ Login screen displayed successfully');
}

// Restore login screen when logging out from app layout
function restoreLoginScreen() {
  console.log('🔄 Restoring original login screen...');

  // Get the original HTML structure from the initial page load
  // For now, we'll reload the page to get back to the login screen
  // This is the safest approach to ensure all original elements are restored
  window.location.reload();
}

// Show main application
async function showMainApplication() {
  console.log('🚀 Showing main application...');

  const loadingScreen = document.getElementById('loading-screen');
  const loginScreen = document.getElementById('login-screen');
  const mainApp = document.getElementById('main-app');

  // Check if all required elements exist
  if (!loadingScreen || !loginScreen || !mainApp) {
    console.error('❌ Required screen elements not found for main app!', {
      loadingScreen: !!loadingScreen,
      loginScreen: !!loginScreen,
      mainApp: !!mainApp
    });
    return;
  }

  loadingScreen.style.display = 'none';
  loginScreen.style.display = 'none';
  mainApp.style.display = 'block';

  // Initialize layout system
  if (window.Layout) {
    console.log('🎨 Initializing Layout System...');
    window.Layout.init();
  }

  // Initialize page router
  if (window.PageRouter) {
    console.log('🔄 Initializing Page Router...');
    window.PageRouter.init();
  }

  // Initialize dashboard
  if (window.Dashboard) {
    console.log('📊 Initializing Dashboard System...');
    // Dashboard system loaded
  }

  // Update user interface
  updateUserInterface();
}

// Update user interface
function updateUserInterface() {
  if (!AIMS.currentUser) {
    console.warn('⚠️ No current user data available for UI update');
    return;
  }

  console.log('🔄 Updating user interface with:', AIMS.currentUser);

  // Update user name in various elements
  const userNameElement = document.getElementById('user-name');
  if (userNameElement) {
    const fullName = `${AIMS.currentUser.first_name || ''} ${AIMS.currentUser.last_name || ''}`.trim();
    userNameElement.textContent = fullName || 'Unknown User';
    console.log('✅ Updated user-name element:', fullName);
  }

  // Update user display name in the dropdown
  const userDisplayName = document.getElementById('user-display-name');
  if (userDisplayName) {
    const fullName = `${AIMS.currentUser.first_name || ''} ${AIMS.currentUser.last_name || ''}`.trim();
    userDisplayName.textContent = fullName || 'Unknown User';
    console.log('✅ Updated user-display-name element:', fullName);
  }

  // Update user role display
  const userRoleElements = document.querySelectorAll('.user-role');
  userRoleElements.forEach(element => {
    const roleNames = {
      'system_admin': 'System Administrator',
      'class_teacher': 'Class Teacher',
      'subject_teacher': 'Subject Teacher'
    };
    element.textContent = roleNames[AIMS.currentUser.role] || 'Unknown Role';
  });

  // Update academic year and term
  updateAcademicInfoDisplay();

}

// Update academic year and term display
function updateAcademicInfoDisplay() {
  const academicYearElement = document.getElementById('current-academic-year');
  const termElement = document.getElementById('current-term');
  const academicPeriodElement = document.getElementById('academic-period');

  // Handle case when no academic data is set up
  if (!AIMS.currentAcademicYear || !AIMS.currentTerm) {
    if (academicYearElement) {
      academicYearElement.textContent = 'Setup Required';
      academicYearElement.classList.add('text-yellow-600');
    }
    if (termElement) {
      termElement.textContent = 'Setup Required';
      termElement.classList.add('text-yellow-600');
    }
    if (academicPeriodElement) {
      academicPeriodElement.textContent = 'Academic Setup Required';
      academicPeriodElement.classList.add('text-yellow-600');
    }
  } else {
    // Normal display when data exists
    if (academicYearElement) {
      academicYearElement.textContent = AIMS.currentAcademicYear;
      academicYearElement.classList.remove('text-yellow-600');
    }
    if (termElement) {
      termElement.textContent = AIMS.currentTerm;
      termElement.classList.remove('text-yellow-600');
    }
    if (academicPeriodElement) {
      academicPeriodElement.textContent = `${AIMS.currentAcademicYear} - ${AIMS.currentTerm}`;
      academicPeriodElement.classList.remove('text-yellow-600');
    }
  }

}

// Helper function to check if current user is the default admin
function isDefaultAdmin() {
  return AIMS.currentUser && AIMS.currentUser.username === 'admin' && AIMS.currentUser.role === 'system_admin';
}

// Check if academic years are set up and show modal if not
async function checkAcademicYearsSetup() {
  try {
    // Don't check if we're currently setting up
    if (window.isSettingUpAcademicYear) {
      console.log('⏳ Academic year setup in progress, skipping check...');
      return;
    }

    console.log('🔍 Checking academic year setup status...');

    // First, check if ANY academic years exist in the database
    let allYears = [];
    let allTerms = [];
    let activeYear = null;
    let activeTerm = null;

    try {
      console.log('🔍 Checking for academic years in database...');
      const yearsResponse = await window.AcademicYearsAPI.getAll();
      allYears = yearsResponse?.data || yearsResponse || [];

      if (allYears.length > 0) {
        activeYear = allYears.find(y => y.is_active);
        console.log('📋 Academic years found:', allYears.length);
        console.log('📅 Active year:', activeYear ? activeYear.name : 'None');
      } else {
        console.log('📋 No academic years found in database - setup required');
        if (isDefaultAdmin()) {
          console.log('🎯 Default admin detected - showing mandatory academic setup modal');
        }
        showAcademicYearSetupModal();
        return;
      }
    } catch (yearError) {
      console.error('❌ Error checking academic years:', yearError);
      // If we can't check the database, don't show the modal
      console.log('⚠️ Cannot verify academic year status due to database error');
      return;
    }

    // Check for terms (only if academic years exist)
    try {
      console.log('🔍 Checking for terms in database...');
      const termsResponse = await window.TermsAPI.getAll();
      allTerms = termsResponse?.data || termsResponse || [];

      if (allTerms.length > 0) {
        activeTerm = allTerms.find(t => t.is_active);
        console.log('📋 Terms found:', allTerms.length);
        console.log('📅 Active term:', activeTerm ? activeTerm.name : 'None');
      } else {
        console.log('📋 No terms found in database - setup required');
        showAcademicYearSetupModal();
        return;
      }
    } catch (termError) {
      console.error('❌ Error checking terms:', termError);
      // If we can't check terms but years exist, show modal to complete setup
      console.log('⚠️ Cannot verify term status - showing setup modal');
      showAcademicYearSetupModal();
      return;
    }

    // If we have both active year and active term, setup is complete
    if (activeYear && activeTerm) {
      console.log('✅ Academic year and terms are already configured');
      console.log('📅 Current Academic Year:', activeYear.name);
      console.log('📅 Current Term:', activeTerm.name);

      // Store the academic info globally
      AIMS.currentAcademicYear = activeYear.name;
      AIMS.currentTerm = activeTerm.name;

      // Update the UI with the current academic info
      updateUserInterface();

      // Update layout component with academic info
      if (window.Layout && window.Layout.updateAcademicInfo) {
        window.Layout.updateAcademicInfo(); // Always fresh from database
      }

      return; // Exit early - setup is complete
    }

    // If we have academic years but no active year, or terms but no active term
    if (allYears.length > 0 && allTerms.length > 0) {
      if (!activeYear) {
        console.log('⚠️ Academic years exist but none are marked as active');
      }
      if (!activeTerm) {
        console.log('⚠️ Terms exist but none are marked as active');
      }
      console.log('🎯 Showing setup modal to activate academic year and term');
      showAcademicYearSetupModal();
      return;
    }

    // If we have academic years but no terms
    if (allYears.length > 0 && allTerms.length === 0) {
      console.log('⚠️ Academic years exist but no terms found');
      console.log('🎯 Showing setup modal to create terms');
      showAcademicYearSetupModal();
      return;
    }

  } catch (error) {
    console.error('❌ Error checking academic years setup:', error);
    // Don't show modal on unexpected errors - let the user navigate normally
    console.log('⚠️ Skipping academic setup check due to unexpected error');
  }
}

// Persistent academic setup checker - runs continuously until setup is complete
function startPersistentAcademicCheck() {
  console.log('🔄 Starting persistent academic setup checker...');

  // Run for all users, but with different intensity for system admins
  if (!AIMS.currentUser) {
    console.log('ℹ️ No user logged in - skipping persistent check');
    return;
  }

  let checkCount = 0;
  const isSystemAdmin = AIMS.currentUser.role === 'system_admin';
  const maxChecks = isSystemAdmin ? 120 : 60; // System admins: 10 minutes, others: 5 minutes

  const persistentCheck = setInterval(async () => {
    checkCount++;

    // Stop checking after max attempts
    if (checkCount > maxChecks) {
      console.log('🕐 Persistent academic check timeout reached');
      clearInterval(persistentCheck);
      return;
    }

    // Skip if setup is in progress
    if (window.isSettingUpAcademicYear) {
      console.log('⏳ Academic setup in progress, skipping persistent check...');
      return;
    }

    // Skip if modal is already open to prevent interrupting user input
    const existingModal = document.getElementById('modal-container');
    if (existingModal && !existingModal.classList.contains('hidden')) {
      console.log('📋 Academic setup modal already open - skipping persistent check to prevent data loss');
      return;
    }

    try {
      // Quick check for active academic year using proper server URL
      const response = await fetch(`${AIMS.serverUrl}/api/academic/years`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('aims_token')}`,
          'Content-Type': 'application/json'
        }
      });
      const result = await response.json();

      if (result.success && result.data && result.data.length > 0) {
        const activeYear = result.data.find(year => year.is_active);

        if (activeYear) {
          console.log('✅ Persistent check: Active academic year found, stopping check');
          clearInterval(persistentCheck);

          // Update global context
          window.AIMS = window.AIMS || {};
          window.AIMS.currentAcademicYear = activeYear.name;
          window.AIMS.currentTerm = activeYear.current_term || 'Term 1';

          // Update UI
          if (window.AuthManager && typeof AuthManager.updateAcademicInfo === 'function') {
            AuthManager.updateAcademicInfo();
          }

          // Show success notification
          if (window.showNotification) {
            showNotification('Academic year setup verified successfully!', 'success');
          }

          return;
        }
      }

      // If no active academic year found, show modal
      if (checkCount % 6 === 0) { // Every 30 seconds
        console.log(`🚨 Persistent check ${checkCount}: No active academic year - showing setup modal`);
        await checkAcademicYearsSetup();
      }

    } catch (error) {
      console.error('Error in persistent academic check:', error);
    }
  }, 5000); // Check every 5 seconds

  // Store interval ID for potential cleanup
  window.persistentAcademicCheckInterval = persistentCheck;
}

// Stop persistent academic check
function stopPersistentAcademicCheck() {
  if (window.persistentAcademicCheckInterval) {
    clearInterval(window.persistentAcademicCheckInterval);
    window.persistentAcademicCheckInterval = null;
    console.log('🛑 Persistent academic check stopped');
  }
}

// Manual trigger for academic setup check - useful for testing and immediate checks
function forceAcademicSetupCheck() {
  console.log('🎯 MANUAL: Force checking academic setup...');

  // Clear any existing setup flag
  window.isSettingUpAcademicYear = false;

  // Stop any existing persistent check
  if (window.stopPersistentAcademicCheck) {
    window.stopPersistentAcademicCheck();
  }

  // Run the check immediately
  checkAcademicYearsSetup();

  // Start persistent check for system admins
  if (AIMS.currentUser && AIMS.currentUser.role === 'system_admin') {
    setTimeout(() => {
      if (window.startPersistentAcademicCheck) {
        window.startPersistentAcademicCheck();
      }
    }, 1000);
  }
}

// Make functions available globally (except createAcademicYear which is defined later)
window.checkAcademicYearsSetup = checkAcademicYearsSetup;
window.showAcademicYearSetupModal = showAcademicYearSetupModal;
window.loadAvailableYears = loadAvailableYears;
window.populateYearDropdown = populateYearDropdown;
window.startPersistentAcademicCheck = startPersistentAcademicCheck;
window.stopPersistentAcademicCheck = stopPersistentAcademicCheck;
window.forceAcademicSetupCheck = forceAcademicSetupCheck;

// Show academic year setup modal
async function showAcademicYearSetupModal() {
  // Check if modal is already open - prevent refreshing while user is inputting data
  const existingModal = document.getElementById('modal-container');
  if (existingModal && !existingModal.classList.contains('hidden')) {
    console.log('📋 Academic setup modal already open - skipping to prevent data loss');
    return;
  }

  // Check if API services are available
  if (!window.AcademicYearsAPI) {
    console.error('❌ AcademicYearsAPI not available - cannot show modal');
    showError('System not ready. Please refresh the page and try again.');
    return;
  }

  // Stop persistent check while modal is open to prevent interference
  if (window.persistentAcademicCheckInterval) {
    console.log('🛑 Stopping persistent check while modal is open');
    clearInterval(window.persistentAcademicCheckInterval);
    window.persistentAcademicCheckInterval = null;
  }

  // Ensure modal container exists
  ensureModalContainer();

  // Load available years for dropdown
  await loadAvailableYears();

  const modalContent = `
    <div class="text-center mb-6">
      <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-calendar-alt text-blue-600 text-2xl"></i>
      </div>
      <h3 class="text-xl font-semibold text-gray-900 mb-2">
        ${isDefaultAdmin() ? 'Welcome to AIMS, Administrator!' : 'CRITICAL: Academic Year Setup Required'}
      </h3>
      <p class="text-gray-600">
        ${isDefaultAdmin()
          ? 'As the system administrator, you must configure the current academic year and terms before the system can be used.'
          : 'The system cannot function without proper academic year and terms configuration. This setup must be completed to continue.'
        }
      </p>
    </div>

    <form id="academic-year-setup-form" class="space-y-6">
      <!-- Academic Year Basic Info -->
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label for="academic-year-name" class="block text-sm font-medium text-gray-700 mb-1">
              Academic Year
            </label>
            <select
              id="academic-year-name"
              name="name"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Select Current Year</option>
              <!-- Years will be populated dynamically -->
            </select>
          </div>

          <div>
            <label for="academic-year-start" class="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              id="academic-year-start"
              name="start_date"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
          </div>

          <div>
            <label for="academic-year-end" class="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              id="academic-year-end"
              name="end_date"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
          </div>
        </div>
      </div>

      <!-- Terms Configuration -->
      <div class="bg-blue-50 rounded-lg p-4">
        <!-- Term 1 -->
        <div class="mb-4 p-3 bg-white rounded border">
          <h5 class="font-medium text-gray-800 mb-3">Term One</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <label for="term1-start" class="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                id="term1-start"
                name="term1_start"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
            </div>
            <div>
              <label for="term1-end" class="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                id="term1-end"
                name="term1_end"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
            </div>
          </div>
        </div>

        <!-- Term 2 -->
        <div class="mb-4 p-3 bg-white rounded border">
          <h5 class="font-medium text-gray-800 mb-3">Term Two</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <label for="term2-start" class="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                id="term2-start"
                name="term2_start"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
            </div>
            <div>
              <label for="term2-end" class="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                id="term2-end"
                name="term2_end"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
            </div>
          </div>
        </div>

        <!-- Term 3 -->
        <div class="mb-4 p-3 bg-white rounded border">
          <h5 class="font-medium text-gray-800 mb-3">Term Three</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <label for="term3-start" class="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                id="term3-start"
                name="term3_start"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
            </div>
            <div>
              <label for="term3-end" class="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                id="term3-end"
                name="term3_end"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
            </div>
          </div>
        </div>

        <!-- Current Term Selection -->
        <div class="mt-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Which term is currently active?
          </label>
          <div class="flex space-x-4">
            <label class="flex items-center">
              <input type="radio" name="active_term" value="1" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
              <span class="ml-2 text-sm text-gray-700">Term 1</span>
            </label>
            <label class="flex items-center">
              <input type="radio" name="active_term" value="2" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
              <span class="ml-2 text-sm text-gray-700">Term 2</span>
            </label>
            <label class="flex items-center">
              <input type="radio" name="active_term" value="3" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
              <span class="ml-2 text-sm text-gray-700">Term 3</span>
            </label>
          </div>
        </div>
      </div>
    </form>
  `;

  const actions = [
    {
      text: 'Create',
      onclick: 'handleAcademicYearSubmit()',
      class: 'bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium text-lg mx-auto'
    }
  ];

  if (window.showModal && typeof window.showModal === 'function') {
    showModal('', modalContent, actions, {
      size: 'lg',
      closable: false, // Modal cannot be closed until setup is complete
      backdrop: 'static', // Prevent closing by clicking outside
      keyboard: false // Prevent ESC key closing
    });
  } else {
    console.error('❌ Modal system not available - ui-helpers.js may not be loaded');
    // Show prominent alert for any user
    alert('CRITICAL: Academic Year Setup Required!\n\nThe system cannot be used without configuring the academic year and terms.');
  }

  // Populate the year dropdown after modal is shown
  setTimeout(() => {
    populateYearDropdown();
  }, 100);
}

// Load available years from database ENUM
async function loadAvailableYears() {
  try {
    // Get years from the academic_years table ENUM
    const response = await fetch(`${AIMS.serverUrl}/api/academic/year-options`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('aims_token')}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      if (result.success && result.data) {
        window.availableYears = result.data;
        console.log('✅ Years loaded from database ENUM:', result.data);
        return;
      } else {
        throw new Error('No years data received');
      }
    } else {
      throw new Error(`API returned ${response.status}`);
    }
  } catch (error) {
    console.warn('⚠️ Failed to load years from database, using fallback:', error);

    // Fallback: use the same years as defined in the schema ENUM
    window.availableYears = ['2025', '2026', '2027', '2028', '2029', '2030'];
    console.log('📅 Using fallback years from schema ENUM');
  }
}

// Populate year dropdown
function populateYearDropdown() {
  const yearSelect = document.getElementById('academic-year-name');
  if (!yearSelect || !window.availableYears) {
    console.warn('⚠️ Year dropdown or available years not found');
    return;
  }

  // Clear existing options except the first one
  while (yearSelect.children.length > 1) {
    yearSelect.removeChild(yearSelect.lastChild);
  }

  // Add year options
  window.availableYears.forEach(year => {
    const option = document.createElement('option');
    option.value = year;
    option.textContent = year;

    // Select current year by default
    if (year === new Date().getFullYear().toString()) {
      option.selected = true;
    }

    yearSelect.appendChild(option);
  });
}

// Custom modal functions removed - using ui-helpers.js modal system

// Handle academic year form submission
function handleAcademicYearSubmit() {
  console.log('🎯 Academic year submit button clicked');

  // Add debugging information
  console.log('🔍 Checking prerequisites...');

  // Check if API is available
  if (!window.AcademicYearsAPI) {
    console.error('❌ AcademicYearsAPI not available');
    showError('API service not available. Please refresh the page and try again.');
    return;
  }

  // Check if form exists
  const form = document.getElementById('academic-year-setup-form');
  if (!form) {
    console.error('❌ Form not found');
    showError('Form not found. Please refresh the page and try again.');
    return;
  }

  console.log('✅ Prerequisites check passed, calling createAcademicYear...');

  // Call the creation function directly
  createAcademicYear();
}

// Create academic year from modal form
async function createAcademicYear() {
  try {
    console.log('🚀 Starting academic year creation...');

    const form = document.getElementById('academic-year-setup-form');
    if (!form) {
      console.error('❌ Form not found');
      showError('Form not found');
      return;
    }

    console.log('✅ Form found:', form);

    // Validate form using HTML5 validation first
    if (!form.checkValidity()) {
      console.warn('⚠️ Form validation failed');
      form.reportValidity();
      return;
    }

    console.log('✅ Form validation passed');

    const formData = new FormData(form);
    console.log('📝 Form data created:', formData);

    // Debug: Log all form data
    for (let [key, value] of formData.entries()) {
      console.log(`📋 Form field: ${key} = ${value}`);
    }

    const activeTerm = formData.get('active_term') || '2';
    console.log('📅 Active term selected:', activeTerm);

    const academicYearData = {
      name: formData.get('name'),
      start_date: formData.get('start_date'),
      end_date: formData.get('end_date'),
      is_active: true,
      terms: [
        {
          name: 'Term 1',
          number: 1,
          start_date: formData.get('term1_start'),
          end_date: formData.get('term1_end'),
          is_active: activeTerm === '1'
        },
        {
          name: 'Term 2',
          number: 2,
          start_date: formData.get('term2_start'),
          end_date: formData.get('term2_end'),
          is_active: activeTerm === '2'
        },
        {
          name: 'Term 3',
          number: 3,
          start_date: formData.get('term3_start'),
          end_date: formData.get('term3_end'),
          is_active: activeTerm === '3'
        }
      ]
    };

    console.log('📊 Academic year data prepared:', academicYearData);

    // Validate required fields
    if (!academicYearData.name || !academicYearData.start_date || !academicYearData.end_date) {
      showError('Please fill in all academic year fields');
      return;
    }

    // Validate term dates
    const missingTermDates = academicYearData.terms.some(term => !term.start_date || !term.end_date);
    if (missingTermDates) {
      showError('Please fill in all term start and end dates');
      return;
    }

    // Validate date order
    if (new Date(academicYearData.start_date) >= new Date(academicYearData.end_date)) {
      showError('Academic year end date must be after start date');
      return;
    }

    // Validate term date order
    for (const term of academicYearData.terms) {
      if (new Date(term.start_date) >= new Date(term.end_date)) {
        showError(`${term.name} end date must be after start date`);
        return;
      }
    }

    // Show loading state - find button by text content since onclick changed
    const submitButtons = document.querySelectorAll('button');
    let submitButton = null;
    for (const button of submitButtons) {
      if (button.textContent.trim() === 'Create') {
        submitButton = button;
        break;
      }
    }

    if (submitButton) {
      submitButton.textContent = 'Creating...';
      submitButton.disabled = true;
    }

    // Set flag to prevent modal from showing during setup
    window.isSettingUpAcademicYear = true;

    // Check if API service is available
    if (!window.AcademicYearsAPI) {
      console.error('❌ AcademicYearsAPI not available');
      showError('API service not available. Please refresh the page and try again.');
      return;
    }

    console.log('📡 Calling AcademicYearsAPI.create with data:', academicYearData);

    // Use the API service to create academic year
    const result = await window.AcademicYearsAPI.create(academicYearData);

    console.log('📡 API response:', result);

    if (result && result.success) {
      if (window.showNotification) {
        showNotification('Academic year and terms created successfully!', 'success');
      }

      // Close modal
      if (window.closeModal) {
        closeModal();
      }

      // Wait a moment for database to be consistent, then refresh
      setTimeout(async () => {
        console.log('🔄 Refreshing system after academic year creation...');

        // Force refresh academic info and update all components
        await refreshAcademicInfo();

        // Update layout with new academic info
        if (window.Layout && window.Layout.updateAcademicInfo) {
          await window.Layout.updateAcademicInfo();
        }

        // Update navigation
        if (window.Navigation && window.Navigation.updateAcademicContext) {
          await window.Navigation.updateAcademicContext();
        }

        // Reload dashboard with new academic context
        if (window.PageRouter) {
          PageRouter.loadPage('dashboard');
        }

        // Update user interface elements
        updateUserInterface();

        // Update layout component with academic info
        if (window.Layout && window.Layout.updateAcademicInfo) {
          window.Layout.updateAcademicInfo(); // Always fresh from database
        }

        // Clear the setup flag
        window.isSettingUpAcademicYear = false;

        console.log('✅ System refresh completed after academic year creation');
      }, 1000); // Increased timeout to ensure database consistency

    } else {
      throw new Error(result?.message || 'Failed to create academic year');
    }
  } catch (error) {
    console.error('Error creating academic year:', error);

    if (window.showNotification) {
      showNotification('Failed to create academic year: ' + error.message, 'error');
    }

    // Clear the setup flag
    window.isSettingUpAcademicYear = false;

    // Reset button - find by text content since onclick changed
    const submitButtons = document.querySelectorAll('button');
    let submitButton = null;
    for (const button of submitButtons) {
      if (button.textContent.trim() === 'Creating...') {
        submitButton = button;
        break;
      }
    }

    if (submitButton) {
      submitButton.textContent = 'Create';
      submitButton.disabled = false;
    }
  }
}

// Make functions available globally
window.createAcademicYear = createAcademicYear;
window.handleAcademicYearSubmit = handleAcademicYearSubmit;

// Refresh academic information from server
async function refreshAcademicInfo() {
  try {
    // Get current academic context from API
    const context = await window.AcademicAPI.getCurrentContext();

    if (context && context.academicYear) {
      AIMS.currentAcademicYear = context.academicYear.name;
      AIMS.currentTerm = context.currentTerm ? context.currentTerm.name : null;

      console.log('✅ Academic info refreshed:', {
        year: AIMS.currentAcademicYear,
        term: AIMS.currentTerm
      });

      updateAcademicInfoDisplay();
    } else {
      console.log('📋 Academic context not yet available - may be still setting up');
      AIMS.currentAcademicYear = null;
      AIMS.currentTerm = null;
      updateAcademicInfoDisplay();
    }
  } catch (error) {
    console.error('Failed to refresh academic info:', error);
    // Fallback to health endpoint
    try {
      const response = await fetch(`${AIMS.serverUrl}/health`);
      if (response.ok) {
        const health = await response.json();
        if (health.database && health.database.stats) {
          AIMS.currentAcademicYear = health.database.stats.currentAcademicYear;
          AIMS.currentTerm = health.database.stats.currentTerm;
          updateAcademicInfoDisplay();
        }
      }
    } catch (healthError) {
      console.error('Failed to get academic info from health endpoint:', healthError);
    }
  }
}





// Load dashboard - now handled by modern navigation system
function loadDashboard() {
  console.log('📊 Loading dashboard...');

  // Use the modern page router to load dashboard
  if (window.PageRouter) {
    PageRouter.loadPage('dashboard');
  }
}

// Utility functions
function showError(message) {
  console.error('Error:', message);
  if (window.showNotification) {
    showNotification(message, 'error');
  } else {
    alert('Error: ' + message);
  }
}

function showSuccess(message) {
  if (window.showNotification) {
    showNotification(message, 'success');
  }
}

// Toggle user menu
function toggleUserMenu() {
  const dropdown = document.getElementById('user-dropdown');
  if (dropdown) {
    dropdown.classList.toggle('hidden');
  }
}

// Close user menu when clicking outside
document.addEventListener('click', function(event) {
  const userMenuButton = event.target.closest('button[onclick="toggleUserMenu()"]');
  const dropdown = document.getElementById('user-dropdown');

  if (dropdown && !dropdown.contains(event.target) && !userMenuButton) {
    dropdown.classList.add('hidden');
  }
});

// Logout function
async function logout() {
  console.log('🚪 Logout initiated...');

  try {
    // Clear user session data
    localStorage.removeItem('aims_token');
    // Note: We don't clear academic setup flag anymore - we rely on database checks
    AIMS.currentUser = null;
    AIMS.isInitialized = false;

    // Hide application menu (Electron)
    if (window.require) {
      const { ipcRenderer } = window.require('electron');
      await ipcRenderer.invoke('logout');
    }

    // Call logout API if available
    try {
      const response = await fetch(`${AIMS.serverUrl}/api/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      console.log('📡 Logout API called:', response.ok ? 'success' : 'failed');
    } catch (apiError) {
      console.warn('⚠️ Logout API call failed:', apiError);
      // Continue with logout even if API call fails
    }

    // Show notification if available
    if (window.showNotification) {
      showNotification('You have been logged out successfully.', 'info');
    }

    console.log('✅ Logout completed, redirecting to login screen...');

    // Show login screen (this will handle the layout restoration)
    showLoginScreen();

  } catch (error) {
    console.error('❌ Logout error:', error);
    // Fallback: reload the page to ensure clean state
    console.log('🔄 Fallback: reloading page...');
    window.location.reload();
  }
}

// Make logout function available globally
window.logout = logout;

// Toggle sidebar collapse (delegate to PageRouter)
function toggleSidebar() {
  if (window.PageRouter && window.PageRouter.toggleSidebar) {
    window.PageRouter.toggleSidebar();
  }
}



// Export functions for global access
window.toggleUserMenu = toggleUserMenu;
window.logout = logout;
window.refreshAcademicInfo = refreshAcademicInfo;
window.updateAcademicInfoDisplay = updateAcademicInfoDisplay;
window.toggleSidebar = toggleSidebar;
window.initializeApplication = initializeApplication;
window.loadDashboard = loadDashboard;
window.showLoginScreen = showLoginScreen;
window.restoreLoginScreen = restoreLoginScreen;
window.hideLoadingScreen = hideLoadingScreen;
window.updateLoadingText = updateLoadingText;

// Verify createAcademicYear function is available globally
if (typeof window.createAcademicYear !== 'function') {
  console.warn('⚠️ createAcademicYear function not found - this may cause issues with the academic year setup modal');
}



// Global utility functions
window.showError = showError;
window.showSuccess = showSuccess;
window.updateUserInterface = updateUserInterface;
window.verifyUIComponents = verifyUIComponents;

// Global modal and UI functions (should be loaded by ui-helpers.js)
if (!window.showModal) {
  console.warn('⚠️ showModal function not loaded from ui-helpers.js - using custom modal fallback');
  // The showCustomAcademicModal function will be used as fallback
}

// closeModal function should be provided by ui-helpers.js

// Simple notification fallback (notification system removed)
if (!window.showNotification) {
  window.showNotification = function(message, type = 'info') {
    // Only show alerts for errors and warnings to avoid spam
    if (type === 'error' || type === 'warning') {
      alert(`${type.toUpperCase()}: ${message}`);
    }
  };
}

// Initialize modal container if it doesn't exist
function ensureModalContainer() {
  if (!document.getElementById('modal-container')) {
    const modalContainer = document.createElement('div');
    modalContainer.id = 'modal-container';
    modalContainer.className = 'fixed inset-0 z-50 hidden bg-black/50 backdrop-blur-sm';
    document.body.appendChild(modalContainer);
  }
}

// Initialize modern components
function initializeModernComponents() {
  console.log('🔧 Initializing components...');

  // Ensure modal container exists
  ensureModalContainer();

  console.log('✅ Modern components initialization complete');

  // Check modern component status after initialization
  setTimeout(() => {
    checkComponentStatus();
  }, 1000);
}

// Component status checker
function checkComponentStatus() {
  const components = {
    'Layout': window.Layout,
    'PageRouter': window.PageRouter,
    'Dashboard': window.Dashboard
  };

  const status = {};
  let allLoaded = true;

  for (const [name, component] of Object.entries(components)) {
    const isLoaded = component !== undefined;
    status[name] = isLoaded;

    if (!isLoaded) {
      console.warn(`❌ ${name} - Missing`);
      allLoaded = false;
    }
  }

  if (!allLoaded) {
    console.warn('⚠️ Some components are missing');
  }

  return status;
}

// Export component functions
window.checkComponentStatus = checkComponentStatus;
