const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');
const { BusinessValidation } = require('../utils/validation');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// =============================================
// ACADEMIC YEARS ROUTES
// =============================================

// Get all academic years
router.get('/years', async (req, res) => {
  try {
    const query = `
      SELECT
        ay.id, ay.name, ay.start_date, ay.end_date, ay.is_active,
        ay.created_at, ay.updated_at,
        creator.first_name as created_by_first_name,
        creator.last_name as created_by_last_name,
        updater.first_name as updated_by_first_name,
        updater.last_name as updated_by_last_name
      FROM academic_years ay
      LEFT JOIN system_users creator ON ay.created_by_id = creator.id
      LEFT JOIN system_users updater ON ay.updated_by_id = updater.id
      ORDER BY ay.name DESC
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get academic years error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve academic years'
    });
  }
});

// Create academic year
router.post('/years', async (req, res) => {
  try {
    const { name, start_date, end_date, is_active, terms } = req.body;
    const userId = req.user.id; // Get from authenticated user

    // Add audit fields to data
    const academicYearData = {
      name, start_date, end_date, is_active: is_active || false,
      created_by_id: userId
    };

    // Validate using business logic
    const validationErrors = await BusinessValidation.validateAcademicYear(academicYearData, false);
    const auditErrors = BusinessValidation.validateAuditFields(academicYearData, false);
    const allErrors = [...validationErrors, ...auditErrors];

    if (allErrors.length > 0) {
      return res.status(400).json(BusinessValidation.formatValidationErrors(allErrors));
    }

    // If setting as active, deactivate other years and update their updated_by_id
    if (is_active) {
      await executeQuery('UPDATE academic_years SET is_active = FALSE, updated_by_id = ?, updated_at = NOW()', [userId]);
    }

    const insertQuery = `
      INSERT INTO academic_years (name, start_date, end_date, is_active, created_by_id, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [name, start_date, end_date, is_active || false, userId]);

    if (!result.success) {
      throw new Error(result.error);
    }

    const academicYearId = result.data.insertId;

    // Create terms if provided
    if (terms && Array.isArray(terms) && terms.length > 0) {
      // Deactivate all existing terms if we're creating new ones
      await executeQuery('UPDATE terms SET is_active = FALSE, updated_by_id = ?, updated_at = NOW()', [userId]);

      for (const term of terms) {
        const { name: termName, number, start_date: termStart, end_date: termEnd, is_active: termActive } = term;

        if (termName && number && termStart && termEnd) {
          const termInsertQuery = `
            INSERT INTO terms (name, number, academic_year_id, start_date, end_date, is_active, created_by_id, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
          `;

          await executeQuery(termInsertQuery, [
            termName,
            number,
            academicYearId,
            termStart,
            termEnd,
            termActive || false,
            userId
          ]);
        }
      }
    }

    // Get the created academic year with terms
    const newYearQuery = `
      SELECT ay.*,
        (SELECT COUNT(*) FROM terms WHERE academic_year_id = ay.id) as terms_count,
        (SELECT COUNT(*) FROM terms WHERE academic_year_id = ay.id AND is_active = TRUE) as active_terms_count
      FROM academic_years ay
      WHERE ay.id = ?
    `;
    const newYearResult = await executeQuery(newYearQuery, [academicYearId]);

    res.status(201).json({
      success: true,
      message: 'Academic year created successfully',
      data: newYearResult.data[0]
    });

  } catch (error) {
    console.error('Create academic year error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create academic year'
    });
  }
});

// Update academic year
router.put('/years/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, start_date, end_date, is_active } = req.body;
    const userId = req.user.id; // Get from authenticated user

    // Check if academic year exists
    const checkQuery = 'SELECT id FROM academic_years WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Academic year not found'
      });
    }

    // If setting as active, deactivate other years
    if (is_active) {
      await executeQuery('UPDATE academic_years SET is_active = FALSE, updated_by_id = ?, updated_at = NOW() WHERE id != ?', [userId, id]);
    }

    const updateQuery = `
      UPDATE academic_years SET
        name = ?, start_date = ?, end_date = ?, is_active = ?, updated_by_id = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [name, start_date, end_date, is_active || false, userId, id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get updated academic year
    const updatedYearQuery = 'SELECT * FROM academic_years WHERE id = ?';
    const updatedYearResult = await executeQuery(updatedYearQuery, [id]);

    res.json({
      success: true,
      message: 'Academic year updated successfully',
      data: updatedYearResult.data[0]
    });

  } catch (error) {
    console.error('Update academic year error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update academic year'
    });
  }
});

// =============================================
// TERMS ROUTES
// =============================================

// Get terms (optionally filtered by academic year)
router.get('/terms', async (req, res) => {
  try {
    const { academic_year_id } = req.query;

    let query = `
      SELECT
        t.*,
        ay.name as academic_year_name,
        creator.first_name as created_by_first_name,
        creator.last_name as created_by_last_name,
        updater.first_name as updated_by_first_name,
        updater.last_name as updated_by_last_name
      FROM terms t
      LEFT JOIN academic_years ay ON t.academic_year_id = ay.id
      LEFT JOIN system_users creator ON t.created_by_id = creator.id
      LEFT JOIN system_users updater ON t.updated_by_id = updater.id
    `;

    let params = [];

    if (academic_year_id) {
      query += ' WHERE t.academic_year_id = ?';
      params.push(academic_year_id);
    }

    query += ' ORDER BY t.academic_year_id DESC, t.number ASC';
    
    const result = await executeQuery(query, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get terms error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve terms'
    });
  }
});

// Create term
router.post('/terms', async (req, res) => {
  try {
    const { name, number, academic_year_id, start_date, end_date, is_active } = req.body;
    const userId = req.user.id; // Get from authenticated user

    // Add audit fields to data
    const termData = {
      name, number, academic_year_id, start_date, end_date, is_active: is_active || false,
      created_by_id: userId
    };

    // Validate using business logic
    const validationErrors = await BusinessValidation.validateTerm(termData, false);
    const auditErrors = BusinessValidation.validateAuditFields(termData, false);
    const allErrors = [...validationErrors, ...auditErrors];

    if (allErrors.length > 0) {
      return res.status(400).json(BusinessValidation.formatValidationErrors(allErrors));
    }

    // If setting as active, deactivate other terms
    if (is_active) {
      await executeQuery('UPDATE terms SET is_active = FALSE, updated_by_id = ?, updated_at = NOW()', [userId]);
    }

    const insertQuery = `
      INSERT INTO terms (name, number, academic_year_id, start_date, end_date, is_active, created_by_id, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [name, number, academic_year_id, start_date, end_date, is_active || false, userId]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created term with academic year info
    const newTermQuery = `
      SELECT t.*, ay.name as academic_year_name
      FROM terms t
      LEFT JOIN academic_years ay ON t.academic_year_id = ay.id
      WHERE t.id = ?
    `;
    const newTermResult = await executeQuery(newTermQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'Term created successfully',
      data: newTermResult.data[0]
    });

  } catch (error) {
    console.error('Create term error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create term'
    });
  }
});

// Update term
router.put('/terms/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, number, academic_year_id, start_date, end_date, is_active } = req.body;
    const userId = req.user.id; // Get from authenticated user

    // Check if term exists
    const checkQuery = 'SELECT id FROM terms WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Term not found'
      });
    }

    // If setting as active, deactivate other terms
    if (is_active) {
      await executeQuery('UPDATE terms SET is_active = FALSE, updated_by_id = ?, updated_at = NOW() WHERE id != ?', [userId, id]);
    }

    const updateQuery = `
      UPDATE terms SET
        name = ?, number = ?, academic_year_id = ?, start_date = ?, end_date = ?, is_active = ?, updated_by_id = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [name, number, academic_year_id, start_date, end_date, is_active || false, userId, id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get updated term
    const updatedTermQuery = `
      SELECT t.*, ay.name as academic_year_name
      FROM terms t
      LEFT JOIN academic_years ay ON t.academic_year_id = ay.id
      WHERE t.id = ?
    `;
    const updatedTermResult = await executeQuery(updatedTermQuery, [id]);

    res.json({
      success: true,
      message: 'Term updated successfully',
      data: updatedTermResult.data[0]
    });

  } catch (error) {
    console.error('Update term error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update term'
    });
  }
});

// Delete term
router.delete('/terms/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if term exists
    const checkQuery = 'SELECT id FROM terms WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);
    
    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Term not found'
      });
    }

    // Delete term
    const deleteQuery = 'DELETE FROM terms WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'Term deleted successfully'
    });

  } catch (error) {
    console.error('Delete term error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete term'
    });
  }
});

// =============================================
// SUBJECTS ROUTES
// =============================================

// Get O-Level subjects with optional filters
router.get('/subjects/o-level', async (req, res) => {
  try {
    const { subject_type } = req.query;

    let query = `
      SELECT
        s.id, s.name, s.short_name, s.subject_type, s.uneb_code, s.exam_papers,
        s.is_active, s.created_at, s.updated_at,
        creator.first_name as created_by_first_name,
        creator.last_name as created_by_last_name,
        updater.first_name as updated_by_first_name,
        updater.last_name as updated_by_last_name
      FROM o_level_subjects s
      LEFT JOIN system_users creator ON s.created_by_id = creator.id
      LEFT JOIN system_users updater ON s.updated_by_id = updater.id
      WHERE 1=1
    `;

    let params = [];

    if (subject_type) {
      query += ' AND s.subject_type = ?';
      params.push(subject_type);
    }


    query += ' ORDER BY s.subject_type, s.name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get O-Level subjects error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve O-Level subjects'
    });
  }
});

// Get A-Level subjects with optional filters
router.get('/subjects/a-level', async (req, res) => {
  try {
    const { subject_type } = req.query;

    let query = `
      SELECT
        s.id, s.name, s.short_name, s.subject_type, s.uneb_code, s.exam_papers,
        s.is_active, s.created_at, s.updated_at,
        creator.first_name as created_by_first_name,
        creator.last_name as created_by_last_name,
        updater.first_name as updated_by_first_name,
        updater.last_name as updated_by_last_name
      FROM a_level_subjects s
      LEFT JOIN system_users creator ON s.created_by_id = creator.id
      LEFT JOIN system_users updater ON s.updated_by_id = updater.id
      WHERE 1=1
    `;

    let params = [];

    if (subject_type) {
      query += ' AND s.subject_type = ?';
      params.push(subject_type);
    }

    // Note: is_compulsory field moved to a_level_subject_classes table

    query += ' ORDER BY s.subject_type, s.name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get A-Level subjects error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level subjects'
    });
  }
});

// Create O-Level subject
router.post('/subjects/o-level', async (req, res) => {
  try {
    const {
      name, short_name, subject_type, uneb_code, exam_papers,
      is_compulsory, compulsory_levels, elective_levels, applicable_levels
    } = req.body;
    const userId = req.user.id; // Get from authenticated user

    // Validate required fields
    if (!name || !short_name || !subject_type) {
      return res.status(400).json({
        success: false,
        message: 'Name, short name, and subject type are required'
      });
    }

    const insertQuery = `
      INSERT INTO o_level_subjects (
        name, short_name, subject_type, uneb_code, exam_papers,
        is_compulsory, compulsory_levels, elective_levels, applicable_levels,
        is_active, created_by_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, TRUE, ?, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [
      name, short_name, subject_type, uneb_code, exam_papers || 1,
      is_compulsory || false, compulsory_levels, elective_levels,
      applicable_levels || 's1,s2,s3,s4', userId
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created subject
    const newSubjectQuery = 'SELECT * FROM o_level_subjects WHERE id = ?';
    const newSubjectResult = await executeQuery(newSubjectQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'O-Level subject created successfully',
      data: newSubjectResult.data[0]
    });

  } catch (error) {
    console.error('Create O-Level subject error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create O-Level subject'
    });
  }
});

// Create A-Level subject
router.post('/subjects/a-level', async (req, res) => {
  try {
    const {
      name, short_name, subject_type, uneb_code, exam_papers,
      is_compulsory, applicable_levels
    } = req.body;
    const userId = req.user.id; // Get from authenticated user

    // Validate required fields
    if (!name || !short_name || !subject_type) {
      return res.status(400).json({
        success: false,
        message: 'Name, short name, and subject type are required'
      });
    }

    const insertQuery = `
      INSERT INTO a_level_subjects (
        name, short_name, subject_type, uneb_code, exam_papers,
        is_compulsory, applicable_levels,
        is_active, created_by_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, TRUE, ?, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [
      name, short_name, subject_type, uneb_code, exam_papers || 1,
      is_compulsory || false, applicable_levels || 's5,s6', userId
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created subject
    const newSubjectQuery = 'SELECT * FROM a_level_subjects WHERE id = ?';
    const newSubjectResult = await executeQuery(newSubjectQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'A-Level subject created successfully',
      data: newSubjectResult.data[0]
    });

  } catch (error) {
    console.error('Create A-Level subject error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create A-Level subject'
    });
  }
});

// Update O-Level subject
router.put('/subjects/o-level/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name, short_name, subject_type, uneb_code, exam_papers,
      is_compulsory, compulsory_levels, elective_levels, applicable_levels, is_active
    } = req.body;
    const userId = req.user.id; // Get from authenticated user

    // Check if subject exists
    const checkQuery = 'SELECT id FROM o_level_subjects WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'O-Level subject not found'
      });
    }

    const updateQuery = `
      UPDATE o_level_subjects SET
        name = ?, short_name = ?, subject_type = ?, uneb_code = ?,
        exam_papers = ?, is_compulsory = ?, compulsory_levels = ?, elective_levels = ?, applicable_levels = ?,
        is_active = ?, updated_by_id = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [
      name, short_name, subject_type, uneb_code, exam_papers,
      is_compulsory, compulsory_levels, elective_levels, applicable_levels, is_active, userId, id
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get updated subject
    const updatedSubjectQuery = 'SELECT * FROM o_level_subjects WHERE id = ?';
    const updatedSubjectResult = await executeQuery(updatedSubjectQuery, [id]);

    res.json({
      success: true,
      message: 'O-Level subject updated successfully',
      data: updatedSubjectResult.data[0]
    });

  } catch (error) {
    console.error('Update O-Level subject error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update O-Level subject'
    });
  }
});

// Update A-Level subject
router.put('/subjects/a-level/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name, short_name, subject_type, uneb_code, exam_papers,
      is_compulsory, applicable_levels, is_active
    } = req.body;
    const userId = req.user.id; // Get from authenticated user

    // Check if subject exists
    const checkQuery = 'SELECT id FROM a_level_subjects WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level subject not found'
      });
    }

    const updateQuery = `
      UPDATE a_level_subjects SET
        name = ?, short_name = ?, subject_type = ?, uneb_code = ?,
        exam_papers = ?, is_compulsory = ?, applicable_levels = ?,
        is_active = ?, updated_by_id = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [
      name, short_name, subject_type, uneb_code, exam_papers,
      is_compulsory, applicable_levels, is_active, userId, id
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get updated subject
    const updatedSubjectQuery = 'SELECT * FROM a_level_subjects WHERE id = ?';
    const updatedSubjectResult = await executeQuery(updatedSubjectQuery, [id]);

    res.json({
      success: true,
      message: 'A-Level subject updated successfully',
      data: updatedSubjectResult.data[0]
    });

  } catch (error) {
    console.error('Update A-Level subject error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update A-Level subject'
    });
  }
});

// =============================================
// LEVELS ROUTES
// =============================================

// Get all education levels (O Level, A Level)
router.get('/levels', async (req, res) => {
  try {
    const query = `
      SELECT id, code, name, display_name, sort_order, is_active
      FROM education_levels
      WHERE is_active = TRUE
      ORDER BY sort_order
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get education levels error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve education levels'
    });
  }
});

// Get all class levels (S.1, S.2, S.3, S.4, S.5, S.6)
router.get('/class-levels', async (req, res) => {
  try {
    const query = `
      SELECT cl.id, cl.code, cl.name, cl.display_name, cl.sort_order, cl.streams_optional, cl.is_active,
             el.code as education_level_code, el.name as education_level_name
      FROM class_levels cl
      JOIN education_levels el ON cl.education_level_id = el.id
      WHERE cl.is_active = TRUE
      ORDER BY cl.sort_order
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get class levels error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve class levels'
    });
  }
});

// =============================================
// STREAMS ROUTES
// =============================================

// Get streams with optional filters
router.get('/streams', async (req, res) => {
  try {
    const { stream_type } = req.query;

    let query = `
      SELECT
        s.id, s.name, s.stream_type, s.is_active, s.created_at, s.updated_at,
        creator.first_name as created_by_first_name,
        creator.last_name as created_by_last_name,
        updater.first_name as updated_by_first_name,
        updater.last_name as updated_by_last_name
      FROM streams s
      LEFT JOIN system_users creator ON s.created_by_id = creator.id
      LEFT JOIN system_users updater ON s.updated_by_id = updater.id
      WHERE s.is_active = TRUE
    `;

    let params = [];

    if (stream_type) {
      query += ' AND s.stream_type = ?';
      params.push(stream_type);
    }

    query += ' ORDER BY s.stream_type, s.name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get streams error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve streams'
    });
  }
});

// Create stream
router.post('/streams', async (req, res) => {
  try {
    const { name, stream_type } = req.body;
    const userId = req.user.id; // Get from authenticated user

    // Validate required fields
    if (!name || !stream_type) {
      return res.status(400).json({
        success: false,
        message: 'Name and stream type are required'
      });
    }

    const insertQuery = `
      INSERT INTO streams (name, stream_type, is_active, created_by_id, created_at, updated_at)
      VALUES (?, ?, TRUE, ?, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [name, stream_type, userId]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created stream
    const newStreamQuery = 'SELECT * FROM streams WHERE id = ?';
    const newStreamResult = await executeQuery(newStreamQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'Stream created successfully',
      data: newStreamResult.data[0]
    });

  } catch (error) {
    console.error('Create stream error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create stream'
    });
  }
});

// Update stream
router.put('/streams/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, stream_type, is_active } = req.body;
    const userId = req.user.id; // Get from authenticated user

    // Check if stream exists
    const checkQuery = 'SELECT id FROM streams WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Stream not found'
      });
    }

    const updateQuery = `
      UPDATE streams SET
        name = ?, stream_type = ?, is_active = ?, updated_by_id = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [name, stream_type, is_active, userId, id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get updated stream
    const updatedStreamQuery = 'SELECT * FROM streams WHERE id = ?';
    const updatedStreamResult = await executeQuery(updatedStreamQuery, [id]);

    res.json({
      success: true,
      message: 'Stream updated successfully',
      data: updatedStreamResult.data[0]
    });

  } catch (error) {
    console.error('Update stream error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update stream'
    });
  }
});

// Delete stream
router.delete('/streams/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if stream exists
    const checkQuery = 'SELECT id FROM streams WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Stream not found'
      });
    }

    // Delete stream
    const deleteQuery = 'DELETE FROM streams WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'Stream deleted successfully'
    });

  } catch (error) {
    console.error('Delete stream error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete stream'
    });
  }
});

// =============================================
// CLASSES ROUTES
// =============================================

// Get classes with optional filters
router.get('/classes', async (req, res) => {
  try {
    const { level } = req.query;

    let query = `
      SELECT
        c.id, c.name, c.current_enrollment, c.is_active, c.created_at, c.updated_at,
        cl.name as class_level_name, cl.sort_order,
        el.code as education_level_code, el.name as education_level_name,
        s.name as stream_name, s.stream_type
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams s ON c.stream_id = s.id
      WHERE c.is_active = TRUE
    `;

    let params = [];

    if (level) {
      query += ' AND el.code = ?';
      params.push(level);
    }

    query += ' ORDER BY cl.sort_order, c.name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get classes error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve classes'
    });
  }
});

// Create class
router.post('/classes', async (req, res) => {
  try {
    const { name, class_level_id, stream_id } = req.body;
    const userId = req.user.id; // Get from authenticated user

    // Validate required fields
    if (!name || !class_level_id) {
      return res.status(400).json({
        success: false,
        message: 'Name and class level are required'
      });
    }

    const insertQuery = `
      INSERT INTO classes (name, class_level_id, stream_id, current_enrollment, is_active, created_by_id, created_at, updated_at)
      VALUES (?, ?, ?, 0, TRUE, ?, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [name, class_level_id, stream_id, userId]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created class with related data
    const newClassQuery = `
      SELECT
        c.*, cl.name as class_level_name, el.name as education_level_name, s.name as stream_name
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams s ON c.stream_id = s.id
      WHERE c.id = ?
    `;
    const newClassResult = await executeQuery(newClassQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'Class created successfully',
      data: newClassResult.data[0]
    });

  } catch (error) {
    console.error('Create class error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create class'
    });
  }
});

// Update class
router.put('/classes/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, class_level_id, stream_id, is_active } = req.body;
    const userId = req.user.id; // Get from authenticated user

    // Check if class exists
    const checkQuery = 'SELECT id FROM classes WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    const updateQuery = `
      UPDATE classes SET
        name = ?, class_level_id = ?, stream_id = ?, is_active = ?, updated_by_id = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [name, class_level_id, stream_id, is_active, userId, id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get updated class
    const updatedClassQuery = `
      SELECT
        c.*, cl.name as class_level_name, el.name as education_level_name, s.name as stream_name
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams s ON c.stream_id = s.id
      WHERE c.id = ?
    `;
    const updatedClassResult = await executeQuery(updatedClassQuery, [id]);

    res.json({
      success: true,
      message: 'Class updated successfully',
      data: updatedClassResult.data[0]
    });

  } catch (error) {
    console.error('Update class error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update class'
    });
  }
});

// Delete class
router.delete('/classes/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if class exists
    const checkQuery = 'SELECT id FROM classes WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    // Delete class
    const deleteQuery = 'DELETE FROM classes WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'Class deleted successfully'
    });

  } catch (error) {
    console.error('Delete class error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete class'
    });
  }
});

// =============================================
// CURRENT CONTEXT ENDPOINT
// =============================================

// Get available years from ENUM
router.get('/year-options', async (req, res) => {
  try {
    // Query to get ENUM values from academic_years table
    const enumQuery = `
      SELECT COLUMN_TYPE
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'academic_years'
      AND COLUMN_NAME = 'name'
    `;

    const result = await executeQuery(enumQuery);

    if (!result.success || result.data.length === 0) {
      throw new Error('Failed to get year options');
    }

    // Parse ENUM values from COLUMN_TYPE
    const columnType = result.data[0].COLUMN_TYPE;
    const enumMatch = columnType.match(/enum\((.+)\)/i);

    if (!enumMatch) {
      throw new Error('Invalid ENUM format');
    }

    // Extract year values and clean them
    const enumValues = enumMatch[1]
      .split(',')
      .map(value => value.replace(/'/g, '').trim())
      .sort();

    res.json({
      success: true,
      data: enumValues,
      message: 'Available years retrieved successfully'
    });

  } catch (error) {
    console.error('Get year options error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get available years',
      error: error.message
    });
  }
});

// Get current academic context (active year and term)
router.get('/current-context', async (req, res) => {
  try {
    // Get active academic year
    const yearQuery = `
      SELECT id, name, start_date, end_date, is_active
      FROM academic_years
      WHERE is_active = TRUE
      LIMIT 1
    `;

    const yearResult = await executeQuery(yearQuery);

    // Get active term
    const termQuery = `
      SELECT id, name, number, academic_year_id, start_date, end_date, is_active
      FROM terms
      WHERE is_active = TRUE
      LIMIT 1
    `;

    const termResult = await executeQuery(termQuery);

    const context = {
      academicYear: yearResult.success && yearResult.data.length > 0 ? yearResult.data[0] : null,
      currentTerm: termResult.success && termResult.data.length > 0 ? termResult.data[0] : null,
      setupRequired: (!yearResult.success || yearResult.data.length === 0) || (!termResult.success || termResult.data.length === 0)
    };

    res.json({
      success: true,
      data: context
    });

  } catch (error) {
    console.error('Get current context error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve current academic context'
    });
  }
});

// =============================================
// GRADING SCALE ROUTES
// =============================================

// Get O-Level grading scale
router.get('/grading-scale/o-level', async (req, res) => {
  try {
    const query = `
      SELECT
        id, competency_level, competency_description,
        min_score, max_score, created_at, updated_at,
        creator.first_name as created_by_first_name,
        creator.last_name as created_by_last_name,
        updater.first_name as updated_by_first_name,
        updater.last_name as updated_by_last_name
      FROM o_level_grading_scale ogs
      LEFT JOIN system_users creator ON ogs.created_by_id = creator.id
      LEFT JOIN system_users updater ON ogs.updated_by_id = updater.id
      ORDER BY ogs.competency_level ASC
    `;

    const result = await executeQuery(query);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data,
      message: 'O-Level grading scale retrieved successfully'
    });

  } catch (error) {
    console.error('Get O-Level grading scale error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve O-Level grading scale',
      error: error.message
    });
  }
});

// Get specific O-Level grading scale entry
router.get('/grading-scale/o-level/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        id, competency_level, competency_description,
        min_score, max_score, created_at, updated_at
      FROM o_level_grading_scale
      WHERE id = ?
    `;

    const result = await executeQuery(query, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Grading scale entry not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0],
      message: 'Grading scale entry retrieved successfully'
    });

  } catch (error) {
    console.error('Get grading scale entry error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve grading scale entry',
      error: error.message
    });
  }
});

// Update O-Level grading scale entry
router.put('/grading-scale/o-level/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { competency_description, min_score, max_score } = req.body;

    // Get current user ID for audit trail
    const currentUserId = req.user?.id || 1; // Default to system admin

    const query = `
      UPDATE o_level_grading_scale
      SET
        competency_description = ?,
        min_score = ?,
        max_score = ?,
        updated_by_id = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const result = await executeQuery(query, [
      competency_description, min_score, max_score, currentUserId, id
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Grading scale entry not found'
      });
    }

    res.json({
      success: true,
      message: 'Grading scale entry updated successfully'
    });

  } catch (error) {
    console.error('Update grading scale entry error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update grading scale entry',
      error: error.message
    });
  }
});

module.exports = router;
